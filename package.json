{"name": "digital-wallet-api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "npx eslint ./src", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-session": "^1.18.1", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "multer": "^2.0.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "zod": "^3.25.75"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/axios": "^0.9.36", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-local": "^1.0.38", "axios": "^1.10.0", "eslint": "^9.30.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}}