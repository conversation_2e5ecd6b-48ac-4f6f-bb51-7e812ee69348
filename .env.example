NODE_ENV="development"
PORT=5000
DB_URL="mongodb://localhost:27017/lms-app"

#JWT
JWT_ACCESS_SECRET="access_secret"
JWT_ACCESS_EXPIRATION="1d"
JWR_REFRESH_SECRET="refresh_secret"
JWT_REFRESH_EXPIRATION="30d"

#BCRYPT
BCRYPT_SALT_ROUNDS=10

#SUPER ADMIN
SUPER_ADMIN_EMAIL="<EMAIL>"
SUPER_ADMIN_PASSWORD="Password1#"

#GOOGLE
GOOGLE_CLIENT_ID=google-client-id
GOOGLE_CLIENT_SECRET=google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:5000/api/v1/auth/google/callback

#PASSPORT
PASSPORT_SECRET="passport_secret"

#EXPRESS SESSION
EXPRESS_SESSION_SECRET="express_session_secret"

#FRONTEND URL
FRONTEND_URL=http://localhost:5173

# SSL_COMMERZ
SSL_STORE_ID=ssl_store_id
SSL_STORE_PASS=ssl_store_pass
SSL_PAYMENT_API=https://sandbox.sslcommerz.com/gwprocess/v3/api.php
SSL_VALIDATION_API=https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php

# SSL COMMERZ BACKEND CALLBACK URLS
SSL_SUCCESS_BACKEND_URL=http://localhost:5000/api/v1/payment/success
SSL_FAIL_BACKEND_URL=http://localhost:5000/api/v1/payment/fail
SSL_CANCEL_BACKEND_URL=http://localhost:5000/api/v1/payment/cancel

# SSL COMMERZ FRONTEND REDIRECT URLS
SSL_SUCCESS_FRONTEND_URL=http://localhost:5173/payment/success
SSL_FAIL_FRONTEND_URL=http://localhost:5173/payment/fail
SSL_CANCEL_FRONTEND_URL=http://localhost:5173/payment/cancel

# CLOUDINARY
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# EMAIL SENDER
SMTP_USER=your_email
SMTP_PASS=your_password
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_FROM=your_email
